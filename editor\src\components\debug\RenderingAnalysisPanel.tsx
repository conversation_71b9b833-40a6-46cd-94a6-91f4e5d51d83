/**
 * 渲染分析面板组件
 * 用于分析和显示渲染性能和统计信息
 */
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Table, Statistic, Typography, Space, Button, Tag } from 'antd';
import { useTranslation } from 'react-i18next';
import { Bar } from '@ant-design/charts';
import {
  BarChartOutlined,
  EyeOutlined,
  AppstoreOutlined,
  ThunderboltOutlined,
  ReloadOutlined,
  WarningOutlined} from '@ant-design/icons';
// 定义本地类型以替代引擎类型
enum PerformanceMetricType {
  FPS = 'fps',
  MEMORY_USAGE = 'memoryUsage',
  RENDER_TIME = 'renderTime',
  CPU_USAGE = 'cpuUsage',
  GPU_USAGE = 'gpuUsage',
  DRAW_CALLS = 'drawCalls',
  TRIANGLES = 'triangles',
  VERTICES = 'vertices'
}

interface PerformanceMetric {
  value: number;
  min?: number;
  max?: number;
  average?: number;
  history?: number[];
  unit?: string;
  threshold?: number;
  exceedsThreshold?: boolean;
}

interface PerformanceReport {
  metrics: { [key: string]: PerformanceMetric };
  bottlenecks?: any[];
  trends?: any[];
  overallScore?: number;
  status?: string;
}

// 模拟性能监控器
class PerformanceMonitor {
  private static instance: PerformanceMonitor;

  static getInstance() {
    if (!this.instance) {
      this.instance = new PerformanceMonitor();
    }
    return this.instance;
  }

  static getReport(): PerformanceReport {
    return {
      metrics: {
        [PerformanceMetricType.FPS]: { value: 60 },
        [PerformanceMetricType.MEMORY_USAGE]: { value: 512 },
        [PerformanceMetricType.RENDER_TIME]: { value: 16.7 },
        [PerformanceMetricType.CPU_USAGE]: { value: 45 },
        [PerformanceMetricType.GPU_USAGE]: { value: 60 },
        [PerformanceMetricType.DRAW_CALLS]: { value: 49 },
        [PerformanceMetricType.TRIANGLES]: { value: 60012 },
        [PerformanceMetricType.VERTICES]: { value: 180036 }
      }
    };
  }
}
import './RenderingAnalysisPanel.less';

const { Title, Text, Paragraph } = Typography;

interface RenderingItem {
  key: string;
  name: string;
  drawCalls: number;
  triangles: number;
  vertices: number;
  renderTime: number;
  visible: boolean;
}

interface RenderingAnalysisProps {
  className?: string;
}

/**
 * 渲染分析面板组件
 */
const RenderingAnalysisPanel: React.FC<RenderingAnalysisProps> = ({ className }) => {
  const { t } = useTranslation();
  
  // 状态
  const [renderingData, setRenderingData] = useState<{
    drawCalls: number;
    triangles: number;
    vertices: number;
    renderTime: number;
    items: RenderingItem[];
  }>({
    drawCalls: 0,
    triangles: 0,
    vertices: 0,
    renderTime: 0,
    items: []});
  
  // 初始化
  useEffect(() => {
    updateRenderingData();
  }, []);
  
  // 更新渲染数据
  const updateRenderingData = () => {
    // 从性能监控器获取渲染数据
    const report = PerformanceMonitor.getReport();
    const drawCalls = report.metrics[PerformanceMetricType.DRAW_CALLS]?.value || 0;
    const triangles = report.metrics[PerformanceMetricType.TRIANGLES]?.value || 0;
    const vertices = report.metrics[PerformanceMetricType.VERTICES]?.value || 0;
    const renderTime = report.metrics[PerformanceMetricType.RENDER_TIME]?.value || 0;
    
    // 模拟渲染项数据（实际应用中应从引擎获取）
    const items: RenderingItem[] = [
      {
        key: '1',
        name: 'Terrain',
        drawCalls: 5,
        triangles: 25000,
        vertices: 75000,
        renderTime: 3.2,
        visible: true},
      {
        key: '2',
        name: 'Character',
        drawCalls: 8,
        triangles: 15000,
        vertices: 45000,
        renderTime: 2.5,
        visible: true},
      {
        key: '3',
        name: 'Vegetation',
        drawCalls: 20,
        triangles: 8000,
        vertices: 24000,
        renderTime: 4.8,
        visible: true},
      {
        key: '4',
        name: 'Buildings',
        drawCalls: 15,
        triangles: 12000,
        vertices: 36000,
        renderTime: 3.5,
        visible: true},
      {
        key: '5',
        name: 'Sky',
        drawCalls: 1,
        triangles: 12,
        vertices: 36,
        renderTime: 0.2,
        visible: true},
    ];
    
    setRenderingData({
      drawCalls,
      triangles,
      vertices,
      renderTime,
      items});
  };
  
  // 表格列定义
  const columns = [
    {
      title: t('debug.rendering.name'),
      dataIndex: 'name',
      key: 'name'},
    {
      title: t('debug.rendering.drawCalls'),
      dataIndex: 'drawCalls',
      key: 'drawCalls',
      sorter: (a: RenderingItem, b: RenderingItem) => a.drawCalls - b.drawCalls},
    {
      title: t('debug.rendering.triangles'),
      dataIndex: 'triangles',
      key: 'triangles',
      render: (triangles: number) => triangles.toLocaleString(),
      sorter: (a: RenderingItem, b: RenderingItem) => a.triangles - b.triangles},
    {
      title: t('debug.rendering.vertices'),
      dataIndex: 'vertices',
      key: 'vertices',
      render: (vertices: number) => vertices.toLocaleString(),
      sorter: (a: RenderingItem, b: RenderingItem) => a.vertices - b.vertices},
    {
      title: t('debug.rendering.renderTime'),
      dataIndex: 'renderTime',
      key: 'renderTime',
      render: (renderTime: number) => `${renderTime.toFixed(2)} ms`,
      sorter: (a: RenderingItem, b: RenderingItem) => a.renderTime - b.renderTime},
    {
      title: t('debug.rendering.visible'),
      dataIndex: 'visible',
      key: 'visible',
      render: (visible: boolean) => (
        <Tag color={visible ? 'green' : 'red'}>
          {visible ? t('debug.rendering.yes') : t('debug.rendering.no')}
        </Tag>
      ),
      filters: [
        { text: t('debug.rendering.yes'), value: true },
        { text: t('debug.rendering.no'), value: false },
      ],
      onFilter: (value: boolean | React.Key, record: RenderingItem) => record.visible === value},
  ];
  
  // 柱状图数据
  const barData = renderingData.items.map(item => ({
    name: item.name,
    value: item.renderTime,
    type: t('debug.rendering.renderTime')})).concat(
    renderingData.items.map(item => ({
      name: item.name,
      value: item.drawCalls,
      type: t('debug.rendering.drawCalls')}))
  );
  
  return (
    <div className={`rendering-analysis-panel ${className || ''}`}>
      <div className="rendering-toolbar">
        <Space>
          <Button icon={<ReloadOutlined />} onClick={updateRenderingData}>
            {t('debug.rendering.refresh')}
          </Button>
        </Space>
      </div>
      
      <div className="rendering-content">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card>
              <Title level={4}>{t('debug.rendering.overview')}</Title>
              <Row gutter={[16, 16]}>
                <Col span={6}>
                  <Statistic
                    title={t('debug.rendering.drawCalls')}
                    value={renderingData.drawCalls}
                    prefix={<BarChartOutlined />}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title={t('debug.rendering.triangles')}
                    value={renderingData.triangles}
                    formatter={(value) => value.toLocaleString()}
                    prefix={<AppstoreOutlined />}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title={t('debug.rendering.vertices')}
                    value={renderingData.vertices}
                    formatter={(value) => value.toLocaleString()}
                    prefix={<AppstoreOutlined />}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title={t('debug.rendering.renderTime')}
                    value={renderingData.renderTime}
                    precision={2}
                    suffix="ms"
                    prefix={<ThunderboltOutlined />}
                    valueStyle={{ 
                      color: renderingData.renderTime > 16 ? '#f5222d' : 
                             renderingData.renderTime > 8 ? '#faad14' : '#52c41a' 
                    }}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
        
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={24}>
            <Card title={t('debug.rendering.performance')}>
              <Bar
                data={barData}
                xField="value"
                yField="name"
                seriesField="type"
                isStack={false}
                isGroup={true}
                legend={{ position: 'top-right' }}
                label={{
                  position: 'right',
                  formatter: (datum: any) => {
                    return datum.type === t('debug.rendering.renderTime') ? 
                      `${datum.value.toFixed(2)} ms` : 
                      datum.value.toString();
                  }}}
              />
            </Card>
          </Col>
        </Row>
        
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={24}>
            <Card title={t('debug.rendering.details')}>
              <Table
                dataSource={renderingData.items}
                columns={columns}
                pagination={{ pageSize: 10 }}
                size="middle"
              />
            </Card>
          </Col>
        </Row>
        
        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={24}>
            <Card title={t('debug.rendering.bottlenecks')}>
              <div className="rendering-bottlenecks">
                {renderingData.drawCalls > 1000 && (
                  <div className="rendering-bottleneck-item">
                    <WarningOutlined className="rendering-warning" />
                    <Text className="rendering-warning">
                      {String(t('debug.rendering.highDrawCalls', { count: renderingData.drawCalls }))}
                    </Text>
                    <Paragraph>
                      {t('debug.rendering.highDrawCallsTip')}
                    </Paragraph>
                  </div>
                )}
                
                {renderingData.renderTime > 16 && (
                  <div className="rendering-bottleneck-item">
                    <WarningOutlined className="rendering-error" />
                    <Text className="rendering-error">
                      {t('debug.rendering.highRenderTime', { time: renderingData.renderTime.toFixed(2) })}
                    </Text>
                    <Paragraph>
                      {t('debug.rendering.highRenderTimeTip')}
                    </Paragraph>
                  </div>
                )}
                
                {renderingData.triangles > 1000000 && (
                  <div className="rendering-bottleneck-item">
                    <WarningOutlined className="rendering-warning" />
                    <Text className="rendering-warning">
                      {String(t('debug.rendering.highTriangles', { count: (renderingData.triangles / 1000000).toFixed(2) }))}
                    </Text>
                    <Paragraph>
                      {t('debug.rendering.highTrianglesTip')}
                    </Paragraph>
                  </div>
                )}
                
                {renderingData.drawCalls <= 1000 && renderingData.renderTime <= 16 && renderingData.triangles <= 1000000 && (
                  <div className="rendering-bottleneck-item">
                    <Text className="rendering-good">
                      {t('debug.rendering.noBottlenecks')}
                    </Text>
                  </div>
                )}
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default RenderingAnalysisPanel;
